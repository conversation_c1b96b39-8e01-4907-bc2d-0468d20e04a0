package actiweb.activo.views;

import actiweb.activo.controllers.*;
import actiweb.activo.models.ClienteModel;
import actiweb.activo.models.FatturaModel;
import actiweb.activo.models.SollecitoModel;

import javax.swing.*;
import javax.swing.event.DocumentEvent;
import javax.swing.event.DocumentListener;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.DefaultTableCellRenderer;
import javax.swing.table.TableColumn;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.sql.PreparedStatement;
import java.awt.RenderingHints;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.NumberFormat;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.Year;
import java.util.*;
import java.util.List;
import java.util.stream.Collectors;

import static actiweb.activo.controllers.Utils.*;

public class MainGuiLayout implements ActionListener {

    final static int winSizeX = 1600;
    final static int winSizeY = 800;
    static JPanel secondPane = new JPanel();
    JPanel mainGlobalPane = new JPanel();
    JPanel paneRow1 = new JPanel();
    JPanel paneRow2 = new JPanel();
    JPanel paneRow3 = new JPanel();
    JPanel panelSolleciti = new JPanel();
    JButton butCaricaFatture;
    JButton butFattNonPagate;
    private JList listClienti;
    private JScrollPane listScrollPane;
    private DefaultListModel listModel;

    public static void main(String[] args) {
        // Schedule a job for the event-dispatching thread:
        // creating and showing this application's GUI.
        javax.swing.SwingUtilities.invokeLater(new Runnable() {
            public void run() {
                MainGuiLayout guiMain = new MainGuiLayout();
                guiMain.createAndShowGUI();
            }
        });
    }

    public static void fatturatoPerMeseGui(Year anno) {
        // carico i solleciti

        JDialog loading = alert("Caricamento in corso..");
        Thread t = new Thread(() -> {
            ArrayList<HashMap<String, Object>> fatturatoPerMeseList = FatturaModel.caricaFatturatoPerMese(anno);
            JPanel fattMensilePane = Fatture.buildFatturatoMensileTableGui(fatturatoPerMeseList);

            /*
             * LinkedHashMap<String, ArrayList<FatturaModel>> fatturatoPerMese =
             * FatturaModel.caricaFatturatoPerMeseDalDb(annoInizio);
             * JPanel fattMensilePane =
             * Fatture.buildFatturatoMensileTableGui(fatturatoPerMese, colToHide);
             */

            // fatturatoMensileDoubleClickHandler(fattMensilePane);

            secondPane.setLayout(new BoxLayout(secondPane, BoxLayout.Y_AXIS));
            secondPane.removeAll();
            secondPane.add(fattMensilePane);
            secondPane.repaint();
            secondPane.revalidate();

            loading.setVisible(false); // Nasconde il dialog
            loading.dispose(); // Distrugge il dialog
        });

        t.start(); // Avvia il thread

    }

    /*
     * private static void fatturatoMensileDoubleClickHandler(JPanel pane) {
     *
     *
     * Component[] components = pane.getComponents();
     * for (Component component : components) {
     * System.out.println(component.toString());
     *
     * if (component instanceof JScrollPane) {
     * JScrollPane jscp = new JScrollPane();
     * jscp = (JScrollPane) component;
     * JTable thisJtable = (JTable) jscp.getViewport().getView();
     * if (thisJtable instanceof JTable) {
     *
     * //JTable thisJtable = new JTable();
     * //thisJtable = (JTable) component;
     *
     * thisJtable.setFocusable(false);
     * JTable finalThisJtable = thisJtable;
     * thisJtable.addMouseListener(new MouseAdapter() {
     * public void mouseClicked(MouseEvent me) {
     * //System.out.println("www");
     * // to detect double click events
     * if ((me.getClickCount() == 2) && (me.getButton() == MouseEvent.BUTTON1)) {
     *
     * JTable target = (JTable) me.getSource();
     * int row = target.getSelectedRow(); // select a row
     * int idClienteIndex =
     * target.getColumn(getColName("IdCliente")).getModelIndex();
     * String idCliente = target.getValueAt(row, idClienteIndex).toString();
     * System.out.println("carico fatture del cliente " + idCliente);
     * //caricaFattureCliente(idCliente, null);
     * }
     *
     * // Crea un DefaultCellEditor personalizzato che non permette la modifica
     * delle celle
     * DefaultCellEditor cellEditor = new DefaultCellEditor(new JTextField()) {
     *
     * @Override
     * public boolean isCellEditable(EventObject event) {
     * return false; // Rende le celle non editabili
     * }
     * };
     *
     * // Imposta l'editor personalizzato sulla JTable per tutte le colonne
     * for (int column = 0; column < finalThisJtable.getColumnCount(); column++) {
     * TableColumn tableColumn = finalThisJtable.getColumnModel().getColumn(column);
     * tableColumn.setCellEditor(cellEditor);
     * }
     * }
     * });
     *
     * }
     * }
     *
     * }
     *
     * }
     */

    public static JPanel openContainerPopup(String popupTitle, double widthPercentage, double heightPercentage) {
        Toolkit toolkit = Toolkit.getDefaultToolkit();
        Dimension screenSize = toolkit.getScreenSize();

        /*
         * double widthPercentage = 0.6; // Impostare la percentuale desiderata per la
         * larghezza
         * double heightPercentage = 0.6; // Impostare la percentuale desiderata per
         * l'altezza
         */

        int width = (int) (screenSize.getWidth() * widthPercentage);
        int height = (int) (screenSize.getHeight() * heightPercentage);

        JFrame dialogFrame = new JFrame(popupTitle);
        dialogFrame.setSize(width, height);
        dialogFrame.setLocationRelativeTo(null);

        JPanel newWinPanel = new JPanel();
        dialogFrame.add(newWinPanel);

        dialogFrame.setVisible(true);
        return newWinPanel;
    }

    public static void caricaFattureCliente(String idCliente, ClienteModel datiCliente) {
        caricaFattureCliente(idCliente, datiCliente, secondPane);
    }

    // se non lo hai a disposizione puoi passare null per datiCliente,
    // in tal caso i dati del cliente verranno ricavati automaticamente dall'id
    public static void caricaFattureCliente(String idCliente, ClienteModel datiCliente, JPanel destPane) {
        JDialog loading = alert("Caricamento in corso..");

        Thread t = new Thread(() -> {
            ClienteModel questoCliente;
            String RagioneSociale = "";
            if (datiCliente == null) {
                // se non ho passato i dati del cliente li ricavo
                try {
                    questoCliente = Clienti.getClienteData(idCliente);
                } catch (SQLException e) {
                    throw new RuntimeException(e);
                }
                if (questoCliente != null) {
                    RagioneSociale = questoCliente.getRagioneSociale();
                }

            } else {
                RagioneSociale = datiCliente.getRagioneSociale();
                questoCliente = datiCliente;
            }

            final String fieldList = "ID,NumDoc,Note,DataFattura,TotalePagato,Totale,Cliente,Tipologia";
            final String SQL = "select " + fieldList + " from ElencoScarico where Cliente=" + idCliente
                    + " ORDER BY DataFattura DESC"; // ORIGINALE

            Utils.JTableCustomColumn customCol = new Utils.JTableCustomColumn();

            customCol.resultSetProcessor = (rs) -> {
                System.out.println("Esecuzione della funzione");
                return rs;
            };
            customCol.colNames.put("ID", getColName("idFattura", true)); // 0
            customCol.colNames.put("NumDoc", getColName("numFattura"));
            customCol.colNames.put("Note", getColName("testoFattura", true));
            customCol.colNames.put("DataFattura", getColName("data"));
            customCol.colNames.put("TotalePagato", getColName("totalePagato"));
            customCol.colNames.put("Totale", getColName("totale"));
            customCol.colNames.put("Cliente", getColName("idCliente", true));
            customCol.colNames.put("Tipologia", getColName("Tipologia", true));
            customCol.colNames.put("Tag", getColName("Tag"));

            customCol.colFuncMap.put("Totale", (importo) -> {
                return Utils.formatMoney(importo.toString());
            });
            customCol.colFuncMap.put("TotalePagato", (importo) -> {
                return Utils.formatMoney(importo.toString());
            });

            customCol.colFuncMap.put("DataFattura", (data) -> {

                return Utils.changeDateStrFormat(data.toString(), "yyyy-MM-dd", "dd/MM/yyyy");

            });

            // JTable fattureTable = putDbQuerySelectResultIntoJTable(SQL, customCol);

            JTable fattureTable = new JTable();

            try (PreparedStatement prepStm = Db.getDbConn().prepareStatement(SQL);
                    ResultSet rs = prepStm.executeQuery()) {

                ArrayList<HashMap<String, Object>> list = resultSetToArrayList(rs);

                // 1. Raccogliamo tutti gli ID delle fatture in una lista
                ArrayList<String> idFattureList = new ArrayList<>();
                for (HashMap<String, Object> fattura : list) {
                    idFattureList.add(fattura.get("ID").toString());
                }

                // 2. Chiamiamo il nuovo metodo per ottenere tutti i dettagli in una sola query
                Map<String, String> mappaDettagli = Fatture.dettagliFattureMultiple(idFattureList);

                for (int i = 0; i < list.size(); i++) {
                    // --- Logica di Taggatura ---
                    String idFatturaCorrente = list.get(i).get("ID").toString();
                    // 3. Recuperiamo il dettaglio dalla mappa invece di fare una query nel ciclo
                    String testoDescrizione = mappaDettagli.getOrDefault(idFatturaCorrente, "");

                    // Usa la logica a blocchi per coerenza con le altre viste.
                    List<TaggaContenuto.TaggedItem> items_tag = TaggaContenuto
                            .getTagsAndPricesFromText(testoDescrizione);
                    Set<String> tags = items_tag.stream()
                            .map(TaggaContenuto.TaggedItem::getTag)
                            .collect(Collectors.toSet());
                    String tagsConcatenati = String.join(", ", new TreeSet<>(tags)); // TreeSet per ordinare
                    list.get(i).put("Tag", tagsConcatenati);

                    // Log di debug per i tag e i prezzi
                    System.out.println("--- Singoli Tag e Prezzi ---");
                    items_tag.forEach(System.out::println);
                    Map<String, Double> totaliPerTag = items_tag.stream()
                            .collect(Collectors.groupingBy(TaggaContenuto.TaggedItem::getTag,
                                    Collectors.summingDouble(TaggaContenuto.TaggedItem::getPrice)));
                    System.out.println("\n--- Totali per Tag ---");
                    totaliPerTag.forEach((tag, totale) -> System.out
                            .println(tag + ": " + String.format(Locale.ITALIAN, "%.2f", totale) + "€"));

                    // --- Fine Logica di Taggatura ---

                    if (list.get(i).get("Tipologia").toString().equals("2")) {
                        Double prezzoNegativo = Double.parseDouble(list.get(i).get("Totale").toString()) * -1;
                        list.get(i).replace("Totale", prezzoNegativo);
                        prezzoNegativo = Double.parseDouble(list.get(i).get("TotalePagato").toString()) * -1;
                        list.get(i).replace("TotalePagato", prezzoNegativo);
                        list.get(i).replace("NumDoc", list.get(i).get("Tipologia").toString() + " (nota di credito)");
                    }
                }

                fattureTable = createJTableFromArrayList(list, customCol);
                fattureTable.setFillsViewportHeight(true);
                fattureTable.setAutoCreateRowSorter(true);
                autoResizeJTableColWidth(fattureTable);

                // Ordina la colonna 1 (la seconda colonna) utilizzando il comparatore
                // personalizzato
                /*
                 * DefaultTableModel model = (DefaultTableModel) fattureTable.getModel();
                 * int dateColIntex =
                 * fattureTable.getColumn(getColName("data")).getModelIndex();
                 * model.getDataVector().sort((a, b) -> new
                 * DateComparator().compare(a.get(dateColIntex), b.get(dateColIntex)));
                 * model.fireTableDataChanged();
                 */

                /*
                 * int dateColIntex =
                 * fattureTable.getColumn(getColName("data")).getModelIndex();
                 * DateColumnSorter.enableSortingByDateColumn(fattureTable, dateColIntex);
                 */

            } catch (SQLException e) {
                throw new RuntimeException(e);
            }

            double importoTotale = 0;
            double importoPagato = 0;
            DefaultTableModel model = (DefaultTableModel) fattureTable.getModel();
            int colIndex = 0;
            int colIndex2 = 0;
            String cVal = "";
            ArrayList<Integer> righeContenentiFattureNonPagate = new ArrayList<>();

            for (int row = 0; row < model.getRowCount(); row++) {
                /*
                 * colIndex = fattureTable.getColumn(getColName("totale")).getModelIndex();
                 * cVal = model.getValueAt(row, colIndex).toString().replace(".", "");
                 * cVal = cVal.replace(",", ".");
                 * importoTotale = importoTotale + Double.parseDouble(cVal);
                 *
                 * colIndex2 =
                 * fattureTable.getColumn(getColName("totalePagato")).getModelIndex();
                 * cVal = model.getValueAt(row, colIndex2).toString().replace(".", "");
                 * cVal = cVal.replace(",", ".");
                 * importoPagato = importoPagato + Double.parseDouble(cVal);
                 */

                colIndex = fattureTable.getColumn(getColName("totale")).getModelIndex();
                colIndex2 = fattureTable.getColumn(getColName("totalePagato")).getModelIndex();

                NumberFormat nf = NumberFormat.getInstance();
                try {
                    double questaFatturaTotale = nf.parse(model.getValueAt(row, colIndex).toString()).doubleValue();
                    double questaFatturaPagato = nf.parse(model.getValueAt(row, colIndex2).toString()).doubleValue();
                    importoTotale = importoTotale + questaFatturaTotale;
                    importoPagato = importoPagato + questaFatturaPagato;
                    if (questaFatturaPagato != questaFatturaTotale) {
                        // aggiungo questa riga a quelle da evidenziare perchè non pagate
                        righeContenentiFattureNonPagate.add(row);
                    }
                } catch (ParseException e) {
                    throw new RuntimeException(e);
                }

            }

            Utils.JTableSpecialCellRenderer cellRenderer = new Utils.JTableSpecialCellRenderer();
            cellRenderer.setEvidColor(GCOLOR1);
            cellRenderer.setRigheDaEvidenziare(righeContenentiFattureNonPagate);
            fattureTable.setDefaultRenderer(Object.class, cellRenderer);

            String importoPagatoStr = Utils.formatMoney(importoPagato);
            String importoTotaleStr = Utils.formatMoney(importoTotale);
            String importoDaDareStr = Utils.formatMoney(importoTotale - importoPagato);

            String statPagamenti = "Pagato <b>" + importoPagatoStr + "€ </b> di <b>" + importoTotaleStr + "€</b>";
            // String statPagamenti="- Totale "+importoTotaleStr+"€";

            statPagamenti = statPagamenti + " - Da dare: <b>" + importoDaDareStr + "€</b>";
            String titoloHeader = "<html><b>" + RagioneSociale + "</b><hr>" + statPagamenti + "</html>";

            JScrollPane scrollPaneForTable = new JScrollPane(fattureTable);

            JPanel panelDashboard = new JPanel();
            panelDashboard.setLayout(new BoxLayout(panelDashboard, BoxLayout.Y_AXIS));

            JPanel panelHeader = new JPanel();
            panelHeader.setLayout(new BoxLayout(panelHeader, BoxLayout.X_AXIS));
            panelHeader.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
            JLabel titoloLabel = new JLabel(titoloHeader);
            Font font = new Font("Arial", Font.PLAIN, 15);
            // Imposta la dimensione del font alla JLabel
            titoloLabel.setFont(font);
            titoloLabel.setAlignmentX(Component.LEFT_ALIGNMENT);

            JButton butSolleciti = new JButton("Invia mail al cliente per le fatture selezionate");

            butSolleciti.setAlignmentX(Component.RIGHT_ALIGNMENT);
            JTable finalFattureTable = fattureTable;
            butSolleciti.addActionListener(e -> {
                HashMap<String, ArrayList<FatturaModel>> raggruppamenti = Solleciti
                        .extractSollecitiDaListaFatture(finalFattureTable);
                Solleciti.clickSollecitiBut(raggruppamenti);
            });

            JButton butStoricoSollecitiCliente = new JButton("Storico Solleciti del cliente");
            butStoricoSollecitiCliente.setAlignmentX(Component.RIGHT_ALIGNMENT);

            String finalRagioneSociale = RagioneSociale;
            butStoricoSollecitiCliente.addActionListener(e -> {
                JPanel storiaPanel = new JPanel();
                storiaPanel.setLayout(new BoxLayout(storiaPanel, BoxLayout.Y_AXIS));
                storiaPanel.setBorder(BorderFactory.createEmptyBorder(10, 5, 10, 15));
                JLabel storiaLabel = new JLabel("Storia solleciti");
                storiaLabel.setAlignmentX(Component.LEFT_ALIGNMENT);

                int[] colToHide = { 0, 4, 5, 6 };
                ArrayList<SollecitoModel> listaSolleciti = SollecitoModel.caricaSollecitiDalDb(idCliente);
                JPanel sollecitiPane = Solleciti.BuildStoricoSollecitiTableGui(listaSolleciti, colToHide);
                // sollecitiPane.setSize(1600, 900);
                JPanel newWinPanel;
                newWinPanel = openContainerPopup("Storia solleciti " + finalRagioneSociale, 0.3, 0.5);
                newWinPanel.add(sollecitiPane);

            });

            /*
             * JPanel butPanel = new JPanel();
             * butPanel.add(butSolleciti);
             * butPanel.add(Box.createHorizontalStrut(5));
             * butPanel.add(butFatturaDiCortesia);
             */
            panelHeader.add(titoloLabel);

            panelHeader.add(Box.createHorizontalGlue());

            // panelHeader.add(butPanel);

            panelHeader.add(butSolleciti);
            // panelHeader.add(Box.createHorizontalStrut(5));
            panelHeader.add(butStoricoSollecitiCliente);

            panelDashboard.add(panelHeader);
            // Aggiunge lo JScrollPane al centro del JPanel
            panelDashboard.add(scrollPaneForTable, BorderLayout.CENTER);

            destPane.setLayout(new BoxLayout(destPane, BoxLayout.Y_AXIS));
            destPane.removeAll();
            destPane.add(panelDashboard);
            destPane.repaint();
            destPane.revalidate();

            /*
             * secondPane.setLayout(new BoxLayout(secondPane, BoxLayout.Y_AXIS));
             * secondPane.removeAll();
             * secondPane.add(panelDashboard);
             * secondPane.repaint();
             * secondPane.revalidate();
             */

            dettagliFatturaGui(fattureTable);
            loading.setVisible(false); // Nasconde il dialog
            loading.dispose(); // Distrugge il dialog
        });

        t.start(); // Avvia il thread

    }

    public static void dettagliFatturaGui(JTable table) {
        int numFattCol, dataFattCol, importoFatCol;

        numFattCol = table.getColumn(getColName("numFattura")).getModelIndex();
        dataFattCol = table.getColumn(getColName("data")).getModelIndex();
        importoFatCol = table.getColumn(getColName("totale")).getModelIndex();

        dettagliFatturaGui(table, numFattCol, dataFattCol, importoFatCol);
    }

    public static void dettagliFatturaGui(JTable table, int numFattCol, int dataFattCol, int importoFatCol) {

        table.setFocusable(false);
        table.addMouseListener(new MouseAdapter() {
            public void mouseClicked(MouseEvent me) {
                // to detect double click events
                if ((me.getClickCount() == 2) && (me.getButton() == MouseEvent.BUTTON1)) {
                    JTable target = (JTable) me.getSource();
                    int row = target.getSelectedRow(); // select a row
                    int column = target.getSelectedColumn(); // select a column

                    int idFatturaIndex = table.getColumn(getColName("idFattura")).getModelIndex();
                    String idFattura = table.getValueAt(row, idFatturaIndex).toString();

                    String titoloFinestra = "Dettaglio fattura n." + table.getValueAt(row, numFattCol).toString();
                    titoloFinestra = titoloFinestra + " del " + table.getValueAt(row, dataFattCol).toString();
                    titoloFinestra = titoloFinestra + " di " + table.getValueAt(row, importoFatCol).toString()
                            + "€ iva inclusa";
                    String dettaglio = "Dettaglio fattura per idfattura=" + idFattura + " non trovato";
                    if (idFattura != null) {
                        try {
                            dettaglio = Fatture.dettaglioFattura(idFattura);
                        } catch (SQLException e) {
                            throw new RuntimeException(e);
                        }
                        // JOptionPane.showMessageDialog(null, dettaglio);
                        // JOptionPane.showMessageDialog(null, dettaglio, titoloFinestra,
                        // JOptionPane.INFORMATION_MESSAGE);
                        JFrame frame = new JFrame(titoloFinestra);
                        JTextArea textArea = new JTextArea();
                        // frame.setLocationRelativeTo(mainGlobalPane);
                        frame.setLocation(500, 250);
                        textArea.setText(dettaglio);
                        textArea.setEditable(false);
                        textArea.setLineWrap(true); // Imposta il line wrap su true

                        JScrollPane scrollPane = new JScrollPane(textArea);

                        JButton butGeneraFattPdf = new JButton("Genera PDF per questa fattura");
                        // butGeneraFattPdf.setMargin(new Insets(10, 20, 10, 20)); // Top, left, bottom,
                        // right

                        JPanel panelDet = new JPanel(new BorderLayout());
                        panelDet.add(scrollPane, BorderLayout.CENTER);
                        panelDet.add(butGeneraFattPdf, BorderLayout.SOUTH);

                        // frame.getContentPane().add(scrollPane);
                        frame.getContentPane().add(panelDet);

                        frame.setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);
                        frame.setSize(600, 450);
                        frame.setVisible(true);

                        String finalTitoloFinestra = titoloFinestra;
                        butGeneraFattPdf.addActionListener(e -> {
                            String singlePdf = "";
                            singlePdf = Fatture.creaPdfDaXml(idFattura);
                            if (!singlePdf.isEmpty()) {
                                System.out.println(singlePdf);
                                JOptionPane.showMessageDialog(null, "File PDF Creato in:\n" + singlePdf,
                                        finalTitoloFinestra, JOptionPane.INFORMATION_MESSAGE);

                            } else {
                                JOptionPane.showMessageDialog(null, "Errore creazione File PDF \n" + singlePdf,
                                        finalTitoloFinestra, JOptionPane.ERROR_MESSAGE);
                            }

                        });

                    }

                    System.out.println("Id fattura=" + table.getValueAt(row, 0));
                }
            }
        });

        // Crea un DefaultCellEditor personalizzato che non permette la modifica delle
        // celle
        DefaultCellEditor cellEditor = new DefaultCellEditor(new JTextField()) {
            @Override
            public boolean isCellEditable(EventObject event) {
                return false; // Rende le celle non editabili
            }
        };

        // Imposta l'editor personalizzato sulla JTable per tutte le colonne
        for (int column = 0; column < table.getColumnCount(); column++) {
            TableColumn tableColumn = table.getColumnModel().getColumn(column);
            tableColumn.setCellEditor(cellEditor);
        }
    }

    public static void fatturatoGlobaleGui(Year annoInizio) {

        /*
         * if (annoInizio == null) {
         * annoInizio = Year.of(2017);
         * }
         */

        JDialog loading = alert("Caricamento in corso..");
        // Year finalAnnoInizio = annoInizio;

        Thread t = new Thread(() -> {

            String baseSQL = "";
            String SQL = "";
            String strPeriodo = "";
            String yearOperator = "";
            String valoreAnno = "";

            if (annoInizio == null) {
                /*
                 * Year annoStart = Year.of(2017);
                 * baseSQL =
                 * "SELECT Cliente, RagioneSociale, SUM(totale) AS SommaPrezzo, COUNT(totale) AS NumFatNonPagate, SUM(totale) - SUM(TotalePagato) AS SommaDaPagare \n"
                 * +
                 * "FROM ElencoScarico t1\n" +
                 * "INNER JOIN Clienti t2\n" +
                 * "ON t1.Cliente = t2.IdCliente\n" +
                 *//* "WHERE TotalePagato <= 0 AND t1.Tipologia!=2 \n" + *//*
                                                                            * "WHERE t1.Tipologia!=2 AND Year(DataFattura)>=%s\n"
                                                                            * +
                                                                            * "GROUP BY Cliente,RagioneSociale\n" +
                                                                            * "ORDER BY SommaPrezzo DESC;\n";
                                                                            * SQL = String.format(baseSQL, annoStart);
                                                                            * strPeriodo = "Sempre";
                                                                            */
                valoreAnno = String.valueOf(Year.of(2017));
                yearOperator = ">=";
                strPeriodo = "Sempre";
            } else {

                valoreAnno = String.valueOf(annoInizio);
                yearOperator = "=";
                strPeriodo = String.valueOf(annoInizio);
            }

            baseSQL = "SELECT Cliente, RagioneSociale, t1.Tipologia, SUM(totale) AS SommaPrezzo, COUNT(totale) AS NumFatNonPagate, SUM(totale) - SUM(TotalePagato) AS SommaDaPagare \n"
                    +
                    "FROM ElencoScarico t1\n" +
                    "INNER JOIN Clienti t2\n" +
                    "ON t1.Cliente = t2.IdCliente\n" +
            /* "WHERE TotalePagato <= 0 AND t1.Tipologia!=2 \n" + */
            /* "WHERE t1.Tipologia!=2 AND Year(DataFattura)" + yearOperator + "%s\n" + */
                    "WHERE Year(DataFattura)" + yearOperator + "%s\n" +
                    "GROUP BY Cliente,RagioneSociale,t1.Tipologia\n" +
                    "ORDER BY SommaPrezzo DESC;\n";
            SQL = String.format(baseSQL, valoreAnno);

            // System.out.println(SQL);

            Utils.JTableCustomColumn customCol = new Utils.JTableCustomColumn();

            customCol.colNames.put("Cliente", "IdCliente<hidden>");
            customCol.colNames.put("RagioneSociale", "Ragione Sociale");
            customCol.colNames.put("SommaPrezzo", "Totale fatturato");
            customCol.colNames.put("SommaDaPagare", "Totale da pagare");
            customCol.colNames.put("NumFatNonPagate", "Numero fatture");

            customCol.colFuncMap.put("SommaPrezzo", (importo) -> {
                return Utils.formatMoney(importo.toString());
            });
            customCol.colFuncMap.put("SommaDaPagare", (importo) -> {
                return Utils.formatMoney(importo.toString());
            });

            // JTable dashboardTable = putDbQuerySelectResultIntoJTable(SQL, customCol);
            JTable dashboardTable;
            PreparedStatement prepStm = null;
            int numeroCattivi = 0;
            int numFatNonPagate = 0;
            Double totaleIncassabile = 0.0;
            Double totaleNoteDiCredito = 0.0;
            try {
                prepStm = Db.getDbConn().prepareStatement(SQL);
                ResultSet rs = prepStm.executeQuery();
                ArrayList<HashMap<String, Object>> list = resultSetToArrayList(rs);

                for (int i = 0; i < list.size(); i++) {

                    Double sommaImportoPerCliente = Double.parseDouble(list.get(i).get("SommaPrezzo").toString());

                    if (list.get(i).get("Tipologia").toString().equals("2")) {
                        /*
                         * Double prezzoNegativo =
                         * Double.parseDouble(list.get(i).get("Totale").toString()) * -1;
                         * list.get(i).replace("Totale", prezzoNegativo);
                         */
                        sommaImportoPerCliente = (-1 * sommaImportoPerCliente);
                    }

                    totaleIncassabile = totaleIncassabile + (sommaImportoPerCliente);
                    numFatNonPagate = numFatNonPagate + Integer.parseInt(list.get(i).get("NumFatNonPagate").toString());
                    numeroCattivi++;
                }

                /*
                 * //sottraggo l'importo delle note di credito
                 * prepStm = Db.getDbConn().prepareStatement(SQL_NOTE_DI_CREDITO);
                 * rs = prepStm.executeQuery();
                 * ArrayList<HashMap<String, Object>> listNoteDiCredito =
                 * resultSetToArrayList(rs);
                 * for (int i = 0; i < listNoteDiCredito.size(); i++) {
                 * Double sommaImportoNoteDiCredito =
                 * Double.parseDouble(listNoteDiCredito.get(i).get("totale").toString());
                 * totaleNoteDiCredito = totaleNoteDiCredito + (sommaImportoNoteDiCredito);
                 * }
                 * System.out.println("totaleIncassabile = " + totaleIncassabile);
                 * System.out.println("totaleNoteDiCredito = " + totaleNoteDiCredito);
                 * totaleIncassabile = totaleIncassabile - totaleNoteDiCredito;
                 * //fine sottraggo l'importo delle note di credito
                 */

                dashboardTable = createJTableFromArrayList(list, customCol);
            } catch (SQLException e) {
                throw new RuntimeException(e);
            }

            autoResizeJTableColWidth(dashboardTable);
            JScrollPane scrollPaneForTable = new JScrollPane(dashboardTable);

            JPanel panelDashboard = new JPanel();
            panelDashboard.setLayout(new BoxLayout(panelDashboard, BoxLayout.Y_AXIS));

            JPanel panelHeader = new JPanel();
            panelHeader.setLayout(new BoxLayout(panelHeader, BoxLayout.X_AXIS));
            panelHeader.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
            String HeaderTitleBase = "Clienti: %s - Importo totale: € %s - Fatture: %s - Periodo: %s";
            String HeaderTitle = String.format(
                    HeaderTitleBase,
                    numeroCattivi,
                    Utils.formatMoney(totaleIncassabile),
                    numFatNonPagate,
                    strPeriodo);
            JLabel titoloLabel = new JLabel(HeaderTitle);
            Font font = new Font("Arial", Font.PLAIN, 20);
            // Imposta la dimensione del font alla JLabel
            titoloLabel.setFont(font);
            titoloLabel.setAlignmentX(Component.LEFT_ALIGNMENT);
            JButton headerBut = new JButton("Seleziona Anno");

            headerBut.setAlignmentX(Component.RIGHT_ALIGNMENT);
            panelHeader.add(titoloLabel);
            panelHeader.add(Box.createHorizontalGlue());
            panelHeader.add(headerBut);

            panelDashboard.add(panelHeader);
            // Aggiunge lo JScrollPane al centro del JPanel
            panelDashboard.add(scrollPaneForTable, BorderLayout.CENTER);

            headerBut.addActionListener(new ActionListener() {
                @Override
                public void actionPerformed(ActionEvent e) {
                    Year annoInizio = askYearPopup("Seleziona l'anno");
                    fatturatoGlobaleGui(annoInizio);
                }
            });

            dashboardDoubleClickHandler(dashboardTable);

            ArrayList<Integer> righeContenentiFattureNonPagate = new ArrayList<>();
            DefaultTableModel model = (DefaultTableModel) dashboardTable.getModel();
            int colIndex = 0;

            for (int row = 0; row < model.getRowCount(); row++) {
                colIndex = dashboardTable.getColumn(customCol.colNames.get("SommaDaPagare")).getModelIndex();

                NumberFormat nf = NumberFormat.getInstance();
                try {
                    double totSommaDaPagare = nf.parse(model.getValueAt(row, colIndex).toString()).doubleValue();

                    if (totSommaDaPagare != 0) {
                        // aggiungo questa riga a quelle da evidenziare perchè non pagate
                        righeContenentiFattureNonPagate.add(row);
                    }
                } catch (ParseException e) {
                    throw new RuntimeException(e);
                }
            }
            Utils.JTableSpecialCellRenderer cellRenderer = new Utils.JTableSpecialCellRenderer();
            cellRenderer.setEvidColor(GCOLOR1);
            cellRenderer.setRigheDaEvidenziare(righeContenentiFattureNonPagate);
            dashboardTable.setDefaultRenderer(Object.class, cellRenderer);

            secondPane.setLayout(new BoxLayout(secondPane, BoxLayout.Y_AXIS));
            secondPane.removeAll();
            secondPane.add(panelDashboard);
            secondPane.repaint();
            secondPane.revalidate();

            loading.setVisible(false); // Nasconde il dialog
            loading.dispose(); // Distrugge il dialog
        });

        t.start(); // Avvia il thread

    }

    private record InvoiceTagDetail(String invoiceId, double price) {
    }

    /**
     * Genera e visualizza una vista di riepilogo del fatturato aggregato per tag
     * per un dato anno.
     * Analizza tutte le fatture dell'anno, le etichetta in base al loro contenuto
     * testuale
     * e mostra una tabella con il totale fatturato per ciascun tag.
     * <p>
     * Facendo doppio click su una riga di questa tabella, viene invocato il metodo
     * {@link #fatturatoPerSingoloTagGui(Year, String)} per mostrare il dettaglio
     * delle fatture che compongono quel totale.
     * 
     * @param anno L'anno di riferimento per l'analisi del fatturato.
     */
    public static void fatturatoPerTagGui(Year anno) {
        if (anno == null) {
            return;
        }
        JDialog loading = alert("Caricamento fatturato per tag per l'anno " + anno + "...");
        Thread t = new Thread(() -> {
            try {
                // Step 1: Get all invoice IDs and their type for the year
                final String fattureSQL = "SELECT ID, Tipologia FROM ElencoScarico WHERE Year(DataFattura) = ?";
                Map<String, Integer> invoiceTypes = new HashMap<>();
                List<String> idFattureList = new ArrayList<>();

                try (PreparedStatement prepStm = Db.getDbConn().prepareStatement(fattureSQL)) {
                    prepStm.setInt(1, anno.getValue());
                    try (ResultSet rs = prepStm.executeQuery()) {
                        while (rs.next()) {
                            String id = rs.getString("ID");
                            int tipologia = rs.getInt("Tipologia");
                            idFattureList.add(id);
                            invoiceTypes.put(id, tipologia);
                        }
                    }
                }

                if (idFattureList.isEmpty()) {
                    SwingUtilities.invokeLater(() -> {
                        secondPane.removeAll();
                        secondPane.add(new JLabel("Nessuna fattura trovata per l'anno " + anno));
                        secondPane.revalidate();
                        secondPane.repaint();
                        loading.dispose();
                    });
                    return;
                }

                // Step 2: Get all details for these invoices
                Map<String, String> mappaDettagli = Fatture.dettagliFattureMultiple(idFattureList);

                // Step 3: Process all details to get tagged items
                List<TaggaContenuto.TaggedItem> allTaggedItems = new ArrayList<>();
                Map<String, List<InvoiceTagDetail>> fatturePerTagConDettagli = new HashMap<>();
                for (String idFattura : idFattureList) {
                    String testoDescrizione = mappaDettagli.getOrDefault(idFattura, "");
                    List<TaggaContenuto.TaggedItem> itemsForInvoice = TaggaContenuto
                            .getTagsAndPricesFromText(testoDescrizione);

                    boolean isCreditNote = invoiceTypes.get(idFattura) == 2;

                    for (TaggaContenuto.TaggedItem item : itemsForInvoice) {
                        double price = item.getPrice();
                        if (isCreditNote) {
                            price = -price;
                        }
                        allTaggedItems.add(new TaggaContenuto.TaggedItem(item.getTag(), price));
                        fatturePerTagConDettagli.computeIfAbsent(item.getTag(), k -> new ArrayList<>())
                                .add(new InvoiceTagDetail(idFattura, price));
                    }
                }

                // Step 4: Aggregate the results
                Map<String, Double> totaliPerTag = allTaggedItems.stream()
                        .collect(Collectors.groupingBy(
                                TaggaContenuto.TaggedItem::getTag,
                                Collectors.summingDouble(TaggaContenuto.TaggedItem::getPrice)));

                // Step 5: Create a JTable to display the results
                final String[] columnNames = { "Tag", "Numero Fatture", "Fatturato Totale", "Totale (IVA 22%)",
                        "% sul Totale" };
                DefaultTableModel model = new DefaultTableModel(columnNames, 0) {
                    @Override
                    public Class<?> getColumnClass(int columnIndex) {
                        // This will help the JTable choose the correct sorter for each column.
                        switch (columnIndex) {
                            case 1: // Numero Fatture
                                return Long.class;
                            case 2: // Fatturato Totale
                            case 3: // Totale (IVA 22%)
                            case 4: // % sul Totale
                                return Double.class;
                            default:
                                return String.class;
                        }
                    }
                };

                double totalRevenue = totaliPerTag.values().stream().mapToDouble(Double::doubleValue).sum();

                new TreeMap<>(totaliPerTag).forEach((tag, totale) -> {
                    long numFatture = fatturePerTagConDettagli.getOrDefault(tag, new ArrayList<>()).stream()
                            .map(InvoiceTagDetail::invoiceId).distinct().count();
                    double totaleConIva = totale * 1.22;
                    double percentage = 0.0;
                    if (totalRevenue != 0) {
                        percentage = (totale / totalRevenue) * 100;
                    }
                    // Add raw numeric data to the model. The rendering will handle formatting.
                    model.addRow(new Object[] { tag, numFatture, totale, totaleConIva, percentage });
                });

                JTable resultTable = new JTable(model) {
                    public boolean isCellEditable(int row, int column) {
                        return false;
                    }
                };
                resultTable.setAutoCreateRowSorter(true);

                // Apply custom renderers to format numbers as currency and percentages for
                // display.
                // This keeps the underlying data as numbers, allowing for correct sorting.
                resultTable.getColumnModel().getColumn(2).setCellRenderer(new CurrencyRenderer());
                resultTable.getColumnModel().getColumn(3).setCellRenderer(new CurrencyRenderer());
                resultTable.getColumnModel().getColumn(4).setCellRenderer(new PercentageRenderer());

                resultTable.addMouseListener(new MouseAdapter() {
                    public void mouseClicked(MouseEvent me) {
                        if (me.getClickCount() == 2) { // to detect double click events
                            JTable target = (JTable) me.getSource();
                            int row = target.convertRowIndexToModel(target.getSelectedRow());
                            String tagSelezionato = (String) target.getModel().getValueAt(row, 0);
                            List<InvoiceTagDetail> dettagliFattureSelezionate = fatturePerTagConDettagli
                                    .get(tagSelezionato);
                            fatturatoPerSingoloTagGui(anno, tagSelezionato, dettagliFattureSelezionate);
                        }
                    }
                });

                double totalRevenueConIva = totalRevenue * 1.22;
                int totalInvoices = idFattureList.size();

                final JLabel selectionSumLabel = new JLabel();
                selectionSumLabel.setFont(new Font("Arial", Font.BOLD, 14));

                final JButton exportCsvButton = new JButton("Esporta CSV selezionati");
                exportCsvButton.setEnabled(false);

                resultTable.getSelectionModel().addListSelectionListener(e -> {
                    if (!e.getValueIsAdjusting()) {
                        double selectedTotal = 0.0;
                        int[] selectedRows = resultTable.getSelectedRows();
                        exportCsvButton.setEnabled(selectedRows.length > 0);

                        if (selectedRows.length > 1) { // Mostra solo se sono selezionate più righe
                            for (int viewRow : selectedRows) {
                                int modelRow = resultTable.convertRowIndexToModel(viewRow);
                                // Read the raw Double from the model, avoiding parsing strings.
                                Object value = resultTable.getModel().getValueAt(modelRow, 2);
                                if (value instanceof Number) {
                                    selectedTotal += ((Number) value).doubleValue();
                                }
                            }
                            double selectedTotalConIva = selectedTotal * 1.22;
                            double percentage = 0.0;
                            if (totalRevenue != 0) {
                                percentage = (selectedTotal / totalRevenue) * 100;
                            }
                            String percentageStr = String.format(Locale.ITALIAN, "%.2f%%", percentage);
                            selectionSumLabel.setText("Totale selezionato: " + Utils.formatMoney(selectedTotal) + " € ("
                                    + Utils.formatMoney(selectedTotalConIva) + " € con IVA) (" + percentageStr + ")");
                        } else {
                            selectionSumLabel.setText(""); // Pulisce la label se 1 o 0 righe sono selezionate
                        }
                    }
                });

                // Step 6: Build the panel and display it
                JPanel resultPanel = new JPanel(new BorderLayout(10, 10));
                resultPanel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));

                JPanel headerPanel = new JPanel(new BorderLayout());
                JLabel titleLabel = new JLabel("<html><center><h2>Fatturato per Tag - Anno " + anno + "</h2>" +
                        "<h3>Totale: " + Utils.formatMoney(totalRevenue) + " € ("
                        + Utils.formatMoney(totalRevenueConIva) + " € con IVA) su " + totalInvoices
                        + " fatture</h3></center></html>");
                titleLabel.setHorizontalAlignment(SwingConstants.CENTER);
                headerPanel.add(titleLabel, BorderLayout.CENTER);

                JButton changeYearButton = new JButton("Cambia Anno");
                changeYearButton.addActionListener(e -> {
                    Year nuovoAnno = askYearPopup("Seleziona un nuovo anno");
                    if (nuovoAnno != null) {
                        fatturatoPerTagGui(nuovoAnno);
                    }
                });

                final JSplitPane splitPane = new JSplitPane(JSplitPane.HORIZONTAL_SPLIT);
                final PieChartPanel pieChartPanel = new PieChartPanel(totaliPerTag);
                pieChartPanel.setVisible(false); // Il pannello del grafico è inizialmente nascosto

                final JButton toggleChartButton = new JButton("Mostra Grafico e Legenda");
                toggleChartButton.addActionListener(e -> {
                    boolean isVisible = pieChartPanel.isVisible();
                    pieChartPanel.setVisible(!isVisible);
                    toggleChartButton.setText(
                            pieChartPanel.isVisible() ? "Nascondi Grafico e Legenda" : "Mostra Grafico e Legenda");
                    splitPane.resetToPreferredSizes();
                    splitPane.setResizeWeight(0.6);
                });

                exportCsvButton.addActionListener(e -> {
                    int[] selectedRows = resultTable.getSelectedRows();
                    if (selectedRows.length == 0) {
                        return;
                    }

                    StringBuilder csvBuilder = new StringBuilder();
                    // Header
                    for (int i = 0; i < resultTable.getColumnCount(); i++) {
                        csvBuilder.append("\"").append(resultTable.getColumnName(i)).append("\"");
                        if (i < resultTable.getColumnCount() - 1) {
                            csvBuilder.append(",");
                        }
                    }
                    csvBuilder.append("\n");

                    // Data
                    for (int viewRow : selectedRows) {
                        int modelRow = resultTable.convertRowIndexToModel(viewRow);
                        for (int i = 0; i < resultTable.getColumnCount(); i++) {
                            Object value = resultTable.getModel().getValueAt(modelRow, i);
                            String cellValue = (value == null) ? "" : value.toString();
                            csvBuilder.append("\"").append(cellValue.replace("\"", "\"\"")).append("\"");
                            if (i < resultTable.getColumnCount() - 1) {
                                csvBuilder.append(",");
                            }
                        }
                        csvBuilder.append("\n");
                    }

                    JTextArea csvTextArea = new JTextArea(csvBuilder.toString());
                    csvTextArea.setEditable(false);
                    csvTextArea.setFont(new Font("Monospaced", Font.PLAIN, 12));

                    JScrollPane scrollPane = new JScrollPane(csvTextArea);

                    JDialog csvDialog = new JDialog((Frame) SwingUtilities.getWindowAncestor(resultTable),
                            "CSV Esportato - Seleziona e Copia (Ctrl+C)", true);
                    csvDialog.setSize(600, 400);
                    csvDialog.setLocationRelativeTo(resultTable);
                    csvDialog.add(scrollPane);
                    csvDialog.setVisible(true);
                });

                JPanel buttonContainer = new JPanel(new FlowLayout(FlowLayout.RIGHT));
                buttonContainer.add(toggleChartButton);
                buttonContainer.add(exportCsvButton);
                buttonContainer.add(changeYearButton);
                headerPanel.add(buttonContainer, BorderLayout.EAST);

                resultPanel.add(headerPanel, BorderLayout.NORTH);

                // Create a JSplitPane to hold the table and the chart
                JScrollPane tableScrollPane = new JScrollPane(resultTable);
                splitPane.setLeftComponent(tableScrollPane);
                splitPane.setRightComponent(pieChartPanel);
                splitPane.setResizeWeight(0.6);
                resultPanel.add(splitPane, BorderLayout.CENTER);
                JPanel footerPanel = new JPanel(new BorderLayout());
                JLabel hintLabel = new JLabel("  (Seleziona più righe per vedere la somma)");
                hintLabel.setFont(new Font("Arial", Font.ITALIC, 12));
                hintLabel.setForeground(Color.GRAY);
                footerPanel.add(hintLabel, BorderLayout.WEST);
                footerPanel.add(selectionSumLabel, BorderLayout.EAST);
                resultPanel.add(footerPanel, BorderLayout.SOUTH);

                SwingUtilities.invokeLater(() -> {
                    autoResizeJTableColWidth(resultTable);
                    secondPane.removeAll();
                    secondPane.add(resultPanel);
                    secondPane.revalidate();
                    secondPane.repaint();
                    loading.dispose();
                });
            } catch (SQLException e) {
                e.printStackTrace();
                SwingUtilities.invokeLater(() -> {
                    loading.dispose();
                    JOptionPane.showMessageDialog(null, "Errore durante il caricamento dei dati dal database.",
                            "Errore Database", JOptionPane.ERROR_MESSAGE);
                });
            }
        });
        t.start();
    }

    /**
     * Renderer for table cells that display currency values.
     * Formats a Number as a string with the Euro symbol and aligns it to the right.
     */
    static class CurrencyRenderer extends DefaultTableCellRenderer {
        public CurrencyRenderer() {
            super();
            setHorizontalAlignment(SwingConstants.RIGHT);
        }

        @Override
        public void setValue(Object value) {
            if (value instanceof Number) {
                setText(Utils.formatMoney(((Number) value).doubleValue()) + " €");
            } else {
                super.setValue(value);
            }
        }
    }

    /**
     * Renderer for table cells that display percentage values.
     * Formats a Number as a string with the percentage symbol and aligns it to the
     * right.
     */
    static class PercentageRenderer extends DefaultTableCellRenderer {
        public PercentageRenderer() {
            super();
            setHorizontalAlignment(SwingConstants.RIGHT);
        }

        @Override
        public void setValue(Object value) {
            if (value instanceof Number) {
                setText(String.format(Locale.ITALIAN, "%.2f%%", ((Number) value).doubleValue()));
            } else {
                super.setValue(value);
            }
        }
    }

    /**
     * Mostra un elenco dettagliato di tutte le singole fatture che corrispondono a
     * un tag specifico in un dato anno.
     * Questo metodo viene tipicamente chiamato in seguito a un doppio click nella
     * vista di riepilogo
     * generata da {@link #fatturatoPerTagGui(Year)}.
     * <p>
     * La finestra popup mostra l'elenco delle fatture filtrate e include nel
     * titolo il totale complessivo.
     * 
     * @param anno L'anno di riferimento per la ricerca.
     * @param tag  Il tag specifico per cui filtrare le fatture.
     */
    public static void fatturatoPerSingoloTagGui(Year anno, String tag, List<InvoiceTagDetail> dettagliFattureDelTag) {
        if (dettagliFattureDelTag == null || dettagliFattureDelTag.isEmpty()) {
            JOptionPane.showMessageDialog(null, "Nessun ID fattura fornito per il tag '" + tag + "'.", "Errore",
                    JOptionPane.WARNING_MESSAGE);
            return;
        }

        // Estrae gli ID univoci e crea una mappa per cercare rapidamente l'importo del
        // tag per ogni fattura.
        Set<String> idFattureDelTag = dettagliFattureDelTag.stream()
                .map(InvoiceTagDetail::invoiceId)
                .collect(Collectors.toSet());
        Map<String, Double> importiTagPerFattura = dettagliFattureDelTag.stream()
                .collect(Collectors.groupingBy(InvoiceTagDetail::invoiceId,
                        Collectors.summingDouble(InvoiceTagDetail::price)));

        JDialog loading = alert("Caricamento fatture per il tag '" + tag + "' per l'anno " + anno + "...");
        Thread t = new Thread(() -> {
            try {
                // Step 1: Build a dynamic query to get data only for the provided invoice IDs.
                StringBuilder sqlBuilder = new StringBuilder(
                        "SELECT e.ID, e.Tipologia, e.NumDoc, e.DataFattura, e.Totale, c.RagioneSociale " +
                                "FROM ElencoScarico e JOIN Clienti c ON e.Cliente = c.IdCliente " +
                                "WHERE e.ID IN (");
                for (int i = 0; i < idFattureDelTag.size(); i++) {
                    sqlBuilder.append(i == 0 ? "?" : ", ?");
                }
                sqlBuilder.append(")");

                List<Map<String, Object>> filteredInvoices = new ArrayList<>();

                try (PreparedStatement ps = Db.getDbConn().prepareStatement(sqlBuilder.toString())) {
                    int paramIndex = 1;
                    for (String id : idFattureDelTag) {
                        ps.setString(paramIndex++, id);
                    }

                    try (ResultSet rs = ps.executeQuery()) {
                        while (rs.next()) {
                            Map<String, Object> invoiceData = new HashMap<>();
                            invoiceData.put("ID", rs.getString("ID"));
                            invoiceData.put("Tipologia", rs.getInt("Tipologia"));
                            invoiceData.put("NumDoc", rs.getString("NumDoc"));
                            invoiceData.put("DataFattura", rs.getDate("DataFattura"));
                            invoiceData.put("Totale", rs.getDouble("Totale"));
                            invoiceData.put("RagioneSociale", rs.getString("RagioneSociale"));
                            filteredInvoices.add(invoiceData);
                        }
                    }
                }

                // Step 2: Get all invoice details
                Map<String, String> invoiceDetails = Fatture.dettagliFattureMultiple(new ArrayList<>(idFattureDelTag));

                // Step 3: Add tags to the invoice data (no filtering needed anymore)
                for (Map<String, Object> invoice : filteredInvoices) {
                    String id = (String) invoice.get("ID");
                    String details = invoiceDetails.getOrDefault(id, "");
                    // Usa la stessa logica di "fatturatoPerTagGui" per coerenza, analizzando i
                    // blocchi.
                    List<TaggaContenuto.TaggedItem> taggedItems = TaggaContenuto.getTagsAndPricesFromText(details);
                    Set<String> tagsForInvoice = taggedItems.stream()
                            .map(TaggaContenuto.TaggedItem::getTag)
                            .collect(Collectors.toSet());
                    invoice.put("Tags", String.join(", ", new TreeSet<>(tagsForInvoice))); // TreeSet per ordinare i tag
                }

                // Step 4: Build and display the results in a new window
                SwingUtilities.invokeLater(() -> {
                    if (filteredInvoices.isEmpty()) {
                        JOptionPane.showMessageDialog(null,
                                "Nessuna fattura trovata per il tag '" + tag + "' nell'anno " + anno,
                                "Nessun Risultato", JOptionPane.INFORMATION_MESSAGE);
                        loading.dispose();
                        return;
                    }

                    String[] columnNames = {
                            getColName("idFattura"),
                            getColName("numFattura"),
                            getColName("data"),
                            "Cliente",
                            "Tags",
                            "Importo Tag",
                            getColName("totale")
                    };
                    DefaultTableModel model = new DefaultTableModel(columnNames, 0) {
                        @Override
                        public boolean isCellEditable(int row, int column) {
                            return false;
                        }
                    };

                    // Il totale complessivo ora è la somma degli importi specifici del tag.
                    double totaleComplessivo = dettagliFattureDelTag.stream().mapToDouble(InvoiceTagDetail::price)
                            .sum();

                    for (Map<String, Object> invoice : filteredInvoices) {
                        double totaleFattura = (double) invoice.get("Totale");
                        if ((int) invoice.get("Tipologia") == 2) { // Nota di credito
                            totaleFattura *= -1;
                        }

                        String idFattura = (String) invoice.get("ID");
                        double importoTag = importiTagPerFattura.getOrDefault(idFattura, 0.0);

                        model.addRow(new Object[] {
                                invoice.get("ID"),
                                invoice.get("NumDoc"),
                                Utils.changeDateStrFormat(invoice.get("DataFattura").toString(), "yyyy-MM-dd",
                                        "dd/MM/yyyy"),
                                invoice.get("RagioneSociale"),
                                invoice.get("Tags"),
                                Utils.formatMoney(importoTag) + " €",
                                Utils.formatMoney(totaleFattura) + " €"
                        });
                    }

                    JTable table = new JTable(model);
                    table.setAutoCreateRowSorter(true);
                    table.getColumn(getColName("idFattura")).setMinWidth(0);
                    table.getColumn(getColName("idFattura")).setMaxWidth(0);

                    dettagliFatturaGui(table);

                    String title = "Fatture per tag '" + tag + "' - Anno " + anno + " (Totale: "
                            + Utils.formatMoney(totaleComplessivo) + " €)";
                    JPanel popupPanel = openContainerPopup(title, 0.7, 0.7);
                    popupPanel.setLayout(new BorderLayout());
                    popupPanel.add(new JScrollPane(table), BorderLayout.CENTER);
                    autoResizeJTableColWidth(table);
                    loading.dispose();
                });

            } catch (SQLException e) {
                e.printStackTrace();
                SwingUtilities.invokeLater(() -> {
                    loading.dispose();
                    JOptionPane.showMessageDialog(null, "Errore durante il caricamento dei dati.", "Errore",
                            JOptionPane.ERROR_MESSAGE);
                });
            }
        });
        t.start();
    }

    /*
     * private void filterItems(JTextField filterField, JList jList, boolean[]
     * visibilityFlags) {
     * String filterText = filterField.getText().toLowerCase();
     * ListModel listModelz = listClienti.getModel();
     * // Impostazione della variabile di controllo per nascondere o mostrare gli
     * elementi
     * //boolean[] visibilityFlags = new boolean[listModelz.getSize()];
     * //System.out.println(listModel);
     * for (int i = 0; i < listModelz.getSize(); i++) {
     * String item = listModelz.getElementAt(i).toString();
     * visibilityFlags[i] = item.toLowerCase().contains(filterText);
     * System.out.println("filterText");
     * System.out.println(filterText);
     * }
     *
     * // Aggiornamento della JList per riflettere la visibilità degli elementi
     * jList.repaint();
     * //return visibilityFlags;
     * }
     *
     * private class FilteredListCellRenderer extends DefaultListCellRenderer {
     * public boolean[] visibilityFlags;
     *
     * public FilteredListCellRenderer(boolean[] visibilityFlags) {
     * this.visibilityFlags = visibilityFlags;
     * }
     *
     * @Override
     * public Component getListCellRendererComponent(JList<?> list, Object value,
     * int index,
     * boolean isSelected, boolean cellHasFocus) {
     * Component renderer = super.getListCellRendererComponent(list, value, index,
     * isSelected, cellHasFocus);
     * boolean isVisible = false; // Default: nascondi l'elemento
     *
     * if (index >= 0 && index < listModel.getSize()) {
     * isVisible = visibilityFlags[index]; // Ottieni la visibilità dell'elemento
     * }
     *
     * renderer.setVisible(isVisible);
     *
     * return renderer;
     * }
     * }
     *
     *
     */

    public static void dashboardDoubleClickHandler(JTable dashboardTable) {
        dashboardTable.setFocusable(false);
        dashboardTable.addMouseListener(new MouseAdapter() {
            public void mouseClicked(MouseEvent me) {
                // System.out.println("www");
                // to detect double click events
                if ((me.getClickCount() == 2) && (me.getButton() == MouseEvent.BUTTON1)) {

                    JTable target = (JTable) me.getSource();
                    int row = target.getSelectedRow(); // select a row
                    int idClienteIndex = target.getColumn(getColName("IdCliente")).getModelIndex();
                    String idCliente = target.getValueAt(row, idClienteIndex).toString();

                    int ragioneSocIndex = target.getColumn(getColName("RagioneSociale")).getModelIndex();
                    String RagioneSociale = target.getValueAt(row, ragioneSocIndex).toString();

                    System.out.println("carico fatture del cliente " + idCliente);

                    JPanel newWinPanel;
                    newWinPanel = openContainerPopup(RagioneSociale, 0.6, 0.6);

                    caricaFattureCliente(idCliente, null, newWinPanel);

                    // caricaFattureCliente(idCliente, null);
                }

                // Crea un DefaultCellEditor personalizzato che non permette la modifica delle
                // celle
                DefaultCellEditor cellEditor = new DefaultCellEditor(new JTextField()) {
                    @Override
                    public boolean isCellEditable(EventObject event) {
                        return false; // Rende le celle non editabili
                    }
                };

                // Imposta l'editor personalizzato sulla JTable per tutte le colonne
                for (int column = 0; column < dashboardTable.getColumnCount(); column++) {
                    TableColumn tableColumn = dashboardTable.getColumnModel().getColumn(column);
                    tableColumn.setCellEditor(cellEditor);
                }
            }
        });
    }

    /*
     * SELECT Cliente, SUM(totale) AS SommaPrezzo, COUNT(totale) AS NumFatNonPagate
     * FROM ElencoScarico
     * WHERE TotalePagato <= 0
     * GROUP BY Cliente
     * ORDER BY Cliente;
     */

    public static Year askYearPopup(String testoPopup) {
        Year[] options = new Year[25];
        LocalDate dataCorrente = LocalDate.now();
        int annoCorrente = dataCorrente.getYear();

        // int fine=2023;
        int i = 0;
        for (int anno = 2017; anno <= annoCorrente; anno++) {
            options[i] = Year.of(anno);
            i++;
        }

        Year n = (Year) JOptionPane.showInputDialog(null, testoPopup, "Anno?", JOptionPane.QUESTION_MESSAGE, null,
                options, options[i - 1]);
        if (n == null) {
            n = Year.of(2017);
        }
        System.out.println(n);
        return n;
    }

    public JComponent BuildGui() {

        // super(new BorderLayout());
        mainGlobalPane.setLayout(new BorderLayout());

        listModel = new DefaultListModel();
        listClienti = new JList(listModel);
        listClienti.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        listClienti.setSelectedIndex(0);
        // listClienti.setVisibleRowCount(35);
        listScrollPane = new JScrollPane(listClienti);

        butFattNonPagate = new JButton("Fatture non pagate");
        butCaricaFatture = new JButton("Fatture del cliente selezionato");
        JTextField filtraClienti = new JTextField("", 30);

        JButton butCattiviPagatori = new JButton("Cattivi pagatori");
        JButton butStoricoSolleciti = new JButton("Storico solleciti");
        JButton butFatturatoPerMese = new JButton("Fatturato per mese");
        JButton butFatturatoGlobale = new JButton("Fatturato globale");
        JButton butFatturatoPerTag = new JButton("Fatturato per Tag");

        JPanel topGuiPanel = new JPanel();
        topGuiPanel.setLayout(new BorderLayout());

        JPanel topGuiPanelLeft = new JPanel();
        topGuiPanelLeft.setLayout(new BoxLayout(topGuiPanelLeft, BoxLayout.LINE_AXIS));
        JPanel topGuiPanelRight = new JPanel();
        topGuiPanelRight.setLayout(new BoxLayout(topGuiPanelRight, BoxLayout.LINE_AXIS));

        topGuiPanelLeft.add(Box.createHorizontalStrut(5));
        topGuiPanelLeft.add(butCaricaFatture);
        topGuiPanelLeft.add(Box.createHorizontalStrut(5));
        topGuiPanelLeft.add(filtraClienti);
        topGuiPanelRight.add(Box.createHorizontalStrut(5));

        topGuiPanelRight.add(butCattiviPagatori);
        topGuiPanelRight.add(Box.createHorizontalStrut(5));
        topGuiPanelRight.add(butFattNonPagate);
        topGuiPanelRight.add(Box.createHorizontalStrut(5));
        topGuiPanelRight.add(butStoricoSolleciti);
        topGuiPanelRight.add(Box.createHorizontalStrut(5));
        topGuiPanelRight.add(butFatturatoPerMese);
        topGuiPanelRight.add(Box.createHorizontalStrut(5));
        topGuiPanelRight.add(butFatturatoGlobale);
        topGuiPanelRight.add(Box.createHorizontalStrut(5));
        topGuiPanelRight.add(butFatturatoPerTag);
        topGuiPanelRight.add(Box.createHorizontalStrut(0));

        topGuiPanel.add(topGuiPanelLeft, BorderLayout.LINE_START);
        topGuiPanel.add(topGuiPanelRight, BorderLayout.CENTER);

        // Create a panel that uses BoxLayout.
        JPanel buttonPane = new JPanel();
        buttonPane.setLayout(new BoxLayout(buttonPane, BoxLayout.LINE_AXIS));
        buttonPane.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));

        buttonPane.add(topGuiPanel);

        /*
         * buttonPane.add(Box.createHorizontalStrut(5));
         * buttonPane.add(butCaricaFatture);
         * buttonPane.add(Box.createHorizontalStrut(5));
         * buttonPane.add(filtraClienti);
         *
         * buttonPane.add(butCattiviPagatori);
         * buttonPane.add(Box.createHorizontalStrut(5));
         * buttonPane.add(butFattNonPagate);
         * buttonPane.add(Box.createHorizontalStrut(5));
         *
         * buttonPane.add(butStoricoSolleciti);
         * buttonPane.add(Box.createHorizontalStrut(5));
         *
         * buttonPane.add(butFatturatoPerMese);
         * buttonPane.add(Box.createHorizontalStrut(5));
         */

        /*
         * buttonPane.add(new JButton("ssasapagate"));
         * buttonPane.add(Box.createHorizontalStrut(5));
         *
         * buttonPane.add(new JButton("ssasaggpagate"));
         * buttonPane.add(Box.createHorizontalStrut(5));
         *
         * buttonPane.add(Box.createHorizontalStrut(436));
         */

        butCaricaFatture.addActionListener(e -> {
            int selVal = listClienti.getSelectedIndex();
            caricaFattureClienteSelezionato(selVal);
        });

        butStoricoSolleciti.addActionListener(e -> {
            StoricoSollecitiGui();
        });

        butCattiviPagatori.addActionListener(e -> {
            loadDashboard();
        });
        butFatturatoPerMese.addActionListener(e -> {
            // Year anno = askYearPopup("Seleziona l'anno per cui mostrare il fatturato");
            // System.out.println(anno);
            LocalDate dataCorrente = LocalDate.now();
            Year anno = Year.of(dataCorrente.getYear());

            fatturatoPerMeseGui(anno);
            // fatturatoPerMeseGui(Year.of(2020));
        });

        butFatturatoGlobale.addActionListener(e -> {
            // Year anno = askYearPopup("Seleziona l'anno");
            // System.out.println(anno);
            // LocalDate dataCorrente = LocalDate.now();
            // Year anno = Year.of(dataCorrente.getYear());

            fatturatoGlobaleGui(null);
            // fatturatoPerMeseGui(Year.of(2020));
        });

        butFatturatoPerTag.addActionListener(e -> {
            fatturatoPerTagGui(Year.now());
        });

        listClienti.setCellRenderer(new filterListCellRenderer(filtraClienti));

        filtraClienti.getDocument().addDocumentListener(new DocumentListener() {
            @Override
            public void insertUpdate(DocumentEvent e) {
                listClienti.updateUI();
            }

            @Override
            public void removeUpdate(DocumentEvent e) {
                listClienti.updateUI();
            }

            @Override
            public void changedUpdate(DocumentEvent e) {
                listClienti.updateUI();
            }
        });

        JPanel sidePanel = new JPanel();
        sidePanel.setLayout(new BoxLayout(sidePanel, BoxLayout.Y_AXIS));
        sidePanel.add(Box.createRigidArea(new Dimension(568, 0)));
        sidePanel.add(listScrollPane);

        mainGlobalPane.add(buttonPane, BorderLayout.PAGE_START);
        mainGlobalPane.add(sidePanel, BorderLayout.LINE_START);
        mainGlobalPane.add(secondPane, BorderLayout.CENTER);
        return mainGlobalPane;

    }

    /**
     * Create the GUI and show it. For thread safety,
     * this method should be invoked from the
     * event-dispatching thread.
     */
    public void createAndShowGUI() {
        // public void createAndShowGUI() {
        // Create and set up the window.
        String serverIp = Db.getDbIp();
        JFrame frame = new JFrame("J-Activo 25.08 (Server " + serverIp + ")");
        frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);

        // Create and set up the content pane.
        JComponent newContentPane = BuildGui();
        newContentPane.setOpaque(true); // content panes must be opaque
        frame.setContentPane(newContentPane);

        Dimension size = new Dimension(winSizeX, winSizeY);

        Toolkit toolkit = Toolkit.getDefaultToolkit();
        Dimension screenSize = toolkit.getScreenSize();
        int width = (int) (screenSize.getWidth() * 0.8);
        int height = (int) (screenSize.getHeight() * 0.8);
        frame.setSize(width, height);
        frame.setMinimumSize(size);

        // listScrollPane.setMaximumSize(size);
        // newContentPane.setPreferredSize(size);
        // newContentPane.setMinimumSize(size);
        frame.setLocationRelativeTo(null);
        // Display the window.
        frame.pack();
        frame.setVisible(true);

        /*
         * Container globalPane=frame.getContentPane();
         * globalPane.setBackground(Color.MAGENTA);
         *
         * //Set up the content pane.
         * addComponentsToPane(globalPane);
         * Dimension size = new Dimension(winSizeX, winSizeY);
         * //listScrollPane.setMaximumSize(size);
         * frame.setPreferredSize(size);
         * frame.setMinimumSize(size);
         *
         * frame.setExtendedState(JFrame.MAXIMIZED_BOTH);
         *
         *
         *
         * //globalPane.setMinimumSize(size);
         * // globalPane.setPreferredSize(size);
         *
         * //mainGlobalPane.setMinimumSize(size);
         * // mainGlobalPane.setPreferredSize(size);
         *
         * //Display the window.
         * frame.pack();
         * frame.setVisible(true);
         * size = new Dimension(250, globalPane.getHeight());
         * System.out.println(size);
         * listClienti.setMinimumSize(size);
         * listScrollPane.setMinimumSize(size);
         * listClienti.setPreferredSize(size);
         * listScrollPane.setPreferredSize(size);
         * paneRow2.setMinimumSize(size);
         * paneRow2.setPreferredSize(size);
         *
         * System.out.println(paneRow1.getHeight());
         * System.out.println(paneRow2.getHeight());
         * System.out.println(paneRow3.getHeight());
         */
    }

    public void loadDashboard() {

        JDialog loading = alert("Caricamento in corso..");
        Thread t = new Thread(() -> {

            final String SQL = "SELECT Cliente, RagioneSociale,t1.Tipologia, SUM(totale) AS SommaPrezzo, COUNT(totale) AS NumFatNonPagate\n"
                    +
                    "FROM ElencoScarico t1\n" +
                    "INNER JOIN Clienti t2\n" +
                    "ON t1.Cliente = t2.IdCliente\n" +
            /* "WHERE TotalePagato <= 0 AND t1.Tipologia!=2 \n" + */
                    "WHERE TotalePagato <= 0 \n" +
                    "GROUP BY Cliente,RagioneSociale,t1.Tipologia\n" +
                    "ORDER BY SommaPrezzo DESC;\n";

            /*
             * final String SQL_NOTE_DI_CREDITO = "SELECT Cliente, totale\n" +
             * "FROM ElencoScarico\n" +
             * "WHERE  Tipologia=2\n" +
             * "GROUP BY Cliente,totale \n" +
             * "ORDER BY totale DESC;";
             */

            Utils.JTableCustomColumn customCol = new Utils.JTableCustomColumn();

            customCol.colNames.put("Cliente", "IdCliente<hidden>");
            customCol.colNames.put("RagioneSociale", "Ragione Sociale");
            customCol.colNames.put("SommaPrezzo", "Totale da pagare");
            customCol.colNames.put("NumFatNonPagate", "Numero fatture non pagate");

            customCol.colFuncMap.put("SommaPrezzo", (importo) -> {
                return Utils.formatMoney(importo.toString());
            });

            // JTable dashboardTable = putDbQuerySelectResultIntoJTable(SQL, customCol);
            JTable dashboardTable;
            PreparedStatement prepStm = null;
            int numeroCattivi = 0;
            int numFatNonPagate = 0;
            Double totaleIncassabile = 0.0;
            Double totaleNoteDiCredito = 0.0;
            try {
                prepStm = Db.getDbConn().prepareStatement(SQL);
                ResultSet rs = prepStm.executeQuery();
                ArrayList<HashMap<String, Object>> list = resultSetToArrayList(rs);

                for (int i = 0; i < list.size(); i++) {

                    Double sommaImportoPerCliente = Double.parseDouble(list.get(i).get("SommaPrezzo").toString());
                    if (list.get(i).get("Tipologia").toString().equals("2")) {
                        /*
                         * Double prezzoNegativo =
                         * Double.parseDouble(list.get(i).get("Totale").toString()) * -1;
                         * list.get(i).replace("Totale", prezzoNegativo);
                         */
                        sommaImportoPerCliente = (-1 * sommaImportoPerCliente);
                    }

                    totaleIncassabile = totaleIncassabile + (sommaImportoPerCliente);
                    numFatNonPagate = numFatNonPagate + Integer.parseInt(list.get(i).get("NumFatNonPagate").toString());
                    numeroCattivi++;
                }

                /*
                 * //sottraggo l'importo delle note di credito
                 * prepStm = Db.getDbConn().prepareStatement(SQL_NOTE_DI_CREDITO);
                 * rs = prepStm.executeQuery();
                 * ArrayList<HashMap<String, Object>> listNoteDiCredito =
                 * resultSetToArrayList(rs);
                 * for (int i = 0; i < listNoteDiCredito.size(); i++) {
                 * Double sommaImportoNoteDiCredito =
                 * Double.parseDouble(listNoteDiCredito.get(i).get("totale").toString());
                 * totaleNoteDiCredito = totaleNoteDiCredito + (sommaImportoNoteDiCredito);
                 * }
                 * System.out.println("totaleIncassabile = " + totaleIncassabile);
                 * System.out.println("totaleNoteDiCredito = " + totaleNoteDiCredito);
                 * totaleIncassabile = totaleIncassabile - totaleNoteDiCredito;
                 * //fine sottraggo l'importo delle note di credito
                 */

                dashboardTable = createJTableFromArrayList(list, customCol);
            } catch (SQLException e) {
                throw new RuntimeException(e);
            }

            autoResizeJTableColWidth(dashboardTable);
            JScrollPane scrollPaneForTable = new JScrollPane(dashboardTable);

            JPanel panelDashboard = new JPanel();
            panelDashboard.setLayout(new BoxLayout(panelDashboard, BoxLayout.Y_AXIS));

            JPanel panelHeader = new JPanel();
            panelHeader.setLayout(new BoxLayout(panelHeader, BoxLayout.X_AXIS));
            panelHeader.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
            String HeaderTitleBase = "Cattivi pagatori: %s - Importo totale: € %s - Fatture: %s";
            String HeaderTitle = String.format(
                    HeaderTitleBase,
                    numeroCattivi,
                    Utils.formatMoney(totaleIncassabile),
                    numFatNonPagate);
            JLabel titoloLabel = new JLabel(HeaderTitle);
            Font font = new Font("Arial", Font.PLAIN, 20);
            // Imposta la dimensione del font alla JLabel
            titoloLabel.setFont(font);
            titoloLabel.setAlignmentX(Component.LEFT_ALIGNMENT);
            JButton headerBut = new JButton("Mostra fatture del cliente selezionato");

            headerBut.setAlignmentX(Component.RIGHT_ALIGNMENT);
            panelHeader.add(titoloLabel);
            panelHeader.add(Box.createHorizontalGlue());
            panelHeader.add(headerBut);

            panelDashboard.add(panelHeader);
            // Aggiunge lo JScrollPane al centro del JPanel
            panelDashboard.add(scrollPaneForTable, BorderLayout.CENTER);

            headerBut.addActionListener(new ActionListener() {
                @Override
                public void actionPerformed(ActionEvent e) {
                    int row = dashboardTable.getSelectedRow();
                    if (row != -1) {
                        int idClienteIndex = dashboardTable.getColumn(getColName("IdCliente")).getModelIndex();
                        String idCliente = dashboardTable.getValueAt(row, idClienteIndex).toString();
                        caricaFattureCliente(idCliente, null);
                    } else {
                        JOptionPane.showMessageDialog(null, "Per favore, seleziona un cliente dalla tabella.",
                                "Nessuna selezione", JOptionPane.INFORMATION_MESSAGE);
                    }
                }
            });

            dashboardDoubleClickHandler(dashboardTable);

            secondPane.setLayout(new BoxLayout(secondPane, BoxLayout.Y_AXIS));
            secondPane.removeAll();
            secondPane.add(panelDashboard);
            secondPane.repaint();
            secondPane.revalidate();

            loading.setVisible(false); // Nasconde il dialog
            loading.dispose(); // Distrugge il dialog
        });

        t.start(); // Avvia il thread

    }

    public void refreshClientList() {

        ArrayList<ClienteModel> listaClienti = null;
        try {
            listaClienti = Clienti.getListaClienti();
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }

        DefaultListModel<String> listModelClienti = new DefaultListModel<>();
        listClienti.setModel(listModelClienti);
        int c = 0;
        for (ClienteModel thisClient : listaClienti) {
            c++;
            String ragSoc = thisClient.getRagioneSociale();
            /*
             * disabilitato perchè poi la ricerca non funziona
             * int maxLength = 50;
             * if (thisClient.getRagioneSociale().length() > maxLength) {
             * ragSoc = thisClient.getRagioneSociale().substring(0, maxLength) + "..";
             * System.out.println("Stringa troncata: " + ragSoc);
             * }
             */
            String nomeCliente = c + ") " + ragSoc + " id=" + thisClient.getIdCliente();
            listModelClienti.addElement(nomeCliente);

        }
        listClienti.setModel(listModelClienti);
        listClienti.setSelectedIndex(0);
        listClienti.ensureIndexIsVisible(0);
        listClienti.repaint();
        listClienti.revalidate();
    }

    public void StoricoSollecitiGui() {
        // carico i solleciti
        int[] colToHide = { 4, 5, 6 };
        ArrayList<SollecitoModel> listaSolleciti = SollecitoModel.caricaSollecitiDalDb("");
        JPanel sollecitiPane = Solleciti.BuildStoricoSollecitiTableGui(listaSolleciti, colToHide);
        secondPane.setLayout(new BoxLayout(secondPane, BoxLayout.Y_AXIS));
        secondPane.removeAll();
        secondPane.add(sollecitiPane);
        secondPane.repaint();
        secondPane.revalidate();
        // mostro i solleciti nelle griglia
        // Crea un array bidimensionale di oggetti per i dati della tabella

    }

    public void caricaFattureClienteSelezionato(int selVal) {
        ArrayList<ClienteModel> listaClienti = null;
        try {
            listaClienti = Clienti.getListaClienti();
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        ClienteModel selCliente = listaClienti.get(selVal);
        caricaFattureCliente(selCliente.getIdCliente(), selCliente);
    }

    public void fattureNonPagateGui(ArrayList<FatturaModel> listaFatture, Year annoInizio) {

        try {

            // ArrayList<FatturaModel> listaFatture =
            // Fatture.listaFattureNonPagate(annoInizio);
            // JProgressBar pBar = new JProgressBar(0, listaFatture.size());
            // pBar.setValue(0);
            // pBar.setStringPainted(true);

            Dimension size = new Dimension(secondPane.getWidth(), secondPane.getHeight());

            String[] columnNames = { "idFattura", "Cliente", "Fattura", "Data Fattura", "Importo ivato da pagare",
                    "Id Cliente" };
            // Object[][] data = {{"Smith", "Snowboarding", "ciao"},{"Smith",
            // "Snowboarding", "ciao"}};
            Object[][] data = new Object[listaFatture.size()][6];
            // JTable table = new JTable(data, columnNames);

            DefaultTableModel model = new DefaultTableModel(data, columnNames);
            JTable table = new JTable(model) {
                @Override
                public Class<?> getColumnClass(int column) {
                    // if (column==4) {return Double.class;} else {return String.class;}
                    return getValueAt(0, column).getClass();
                }
            };

            JScrollPane scrollPaneForTable = new JScrollPane(table);
            table.setFillsViewportHeight(true);
            table.setAutoCreateRowSorter(true);
            table.setSelectionMode(ListSelectionModel.MULTIPLE_INTERVAL_SELECTION);

            secondPane.setLayout(new BoxLayout(secondPane, BoxLayout.Y_AXIS));
            secondPane.removeAll();

            JButton header = new JButton();

            ButtonBar butBar = new ButtonBar(secondPane);
            butBar.onclick(header, null);
            JButton butSolleciti = new JButton("Invia sollecito per le fatture selezionate");
            // butBar.onclick(butSolleciti, new onclickSolleciti());
            butBar.onclick(butSolleciti, null);
            butSolleciti.addActionListener(e -> {
                HashMap<String, ArrayList<FatturaModel>> raggruppamenti = Solleciti.extractSollecitiDaNonPagate(table);
                Solleciti.clickSollecitiBut(raggruppamenti);
            });

            secondPane.add(scrollPaneForTable);
            secondPane.repaint();
            secondPane.revalidate();

            int row = 0;
            ClienteModel tempCliente;
            String tempRagSoc;
            double importoTotale = 0;
            for (FatturaModel fat : listaFatture) {
                // pBar.setValue(row + 1);
                tempRagSoc = "not found for id=" + fat.getIdCliente();
                // System.out.println("Fattura " + fat.getNumFattura());
                tempCliente = Clienti.getClienteData(fat.getIdCliente());
                if (tempCliente != null) {
                    tempRagSoc = tempCliente.getRagioneSociale();
                }
                table.setValueAt(fat.getIdFattura(), row, 0);
                table.setValueAt(tempRagSoc, row, 1);
                table.setValueAt(fat.getNumFattura(), row, 2);
                table.setValueAt(fat.getDataFattura(), row, 3);
                table.setValueAt(fat.getPrezzoTotaleFattura(), row, 4);
                table.setValueAt(fat.getIdCliente(), row, 5);
                importoTotale = importoTotale + fat.getPrezzoTotaleFattura();
                row++;
            }

            header.setText(row + " fatture trovate a partire dall'anno " + annoInizio + " - Totale: "
                    + String.format("%.2f", importoTotale) + " euro");
            // Nasconde la colonna 0 dove c'è l'id fattura
            table.getColumnModel().getColumn(0).setMinWidth(0);
            table.getColumnModel().getColumn(0).setMaxWidth(0);
            table.getColumnModel().getColumn(0).setWidth(0);

            table.getColumnModel().getColumn(5).setMinWidth(0);
            table.getColumnModel().getColumn(5).setMaxWidth(0);
            table.getColumnModel().getColumn(5).setWidth(0);

            autoResizeJTableColWidth(table);

            dettagliFatturaGui(table, 2, 3, 4);
            // handlerSolleciti(table);

        } catch (SQLException ex) {
            throw new RuntimeException(ex);// dd
        }

    }

    public void setGuiActionListener() {

        listClienti.addMouseListener(new MouseAdapter() {
            public void mouseClicked(MouseEvent me) {
                // to detect double click events
                if ((me.getClickCount() == 2) && (me.getButton() == MouseEvent.BUTTON1)) {
                    // System.out.println("quiii=" + listClienti.getSelectedIndex());
                    int selVal = listClienti.getSelectedIndex();
                    caricaFattureClienteSelezionato(selVal);
                }
            }
        });

        // ho cliccato sul pulsante per caricare le fatture del cliente selezionato
        /*
         * butCaricaFatture.addActionListener(new ActionListener() {
         *
         * @Override
         * public void actionPerformed(ActionEvent e) {
         * int selVal = listClienti.getSelectedIndex();
         * caricaFattureClienteSelezionato(selVal);
         *
         * }
         * });
         */

        butFattNonPagate.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                Year annoInizio = askYearPopup(
                        "Seleziona l'anno da cui iniziare la ricerca delle fatture non pagate e premi <OK>\nPremi <Annulla> per cercare tutte le fatture dall'inizio");

                JDialog loading = alert("Caricamento Fatture non pagate a partire dall'anno " + annoInizio);

                Thread t = new Thread(() -> {

                    // do stuff
                    try {
                        ArrayList<FatturaModel> listaFatture;
                        final String fieldList = "ID,NumDoc,Note,DataFattura,TotalePagato,totale,Cliente,tipologia,imponibile";
                        final String SQL = "SELECT " + fieldList
                                + " from ElencoScarico where TotalePagato<=0 and Year(DataFattura)>=" + annoInizio
                                + " ORDER BY Cliente";
                        // final String SQL = "SELECT * from ElencoScarico where TotalePagato<=0 and
                        // Year(DataFattura)>=" + annoInizio + " ORDER BY Cliente";
                        // System.out.println(SQL);

                        listaFatture = Fatture.listaFattureNonPagate(SQL, annoInizio);
                        System.out.println("listaFatture ottenuta");
                        fattureNonPagateGui(listaFatture, annoInizio);
                        System.out.println("fattureNonPagateGui");

                    } catch (SQLException ex) {
                        throw new RuntimeException(ex);
                    }

                    loading.setVisible(false); // Nasconde il dialog
                    loading.dispose(); // Distrugge il dialog

                });
                t.start(); // Avvia il thread

                /*
                 * try {
                 * final String SQL =
                 * "SELECT  * from ElencoScarico where TotalePagato<=0 and Year(DataFattura)>="
                 * + annoInizio + " ORDER BY Cliente";
                 * ArrayList<FatturaModel> listaFatture = Fatture.listaFattureNonPagate(SQL,
                 * annoInizio);
                 * fattureNonPagateGui(listaFatture, annoInizio);
                 * } catch (SQLException ex) {
                 * throw new RuntimeException(ex);
                 * }
                 */

            }
        });

    }

    /**
     * @param actionEvent
     */
    @Override
    public void actionPerformed(ActionEvent actionEvent) {

    }

    // Implementa il tuo rendere personalizzato per nascondere i valori non
    // corrispondenti
    private static class filterListCellRenderer extends DefaultListCellRenderer {

        JTextField filtro;
        private int height = 0;

        public filterListCellRenderer(JTextField campoFiltro) {
            this.filtro = campoFiltro;
        }

        @Override
        public Component getListCellRendererComponent(JList<?> list, Object value, int index, boolean isSelected,
                boolean cellHasFocus) {

            Component renderer = super.getListCellRendererComponent(list, value, index, isSelected, cellHasFocus);
            // String searchText = "Azi";
            String searchText = this.filtro.getText();
            // System.out.println("searchText = " + searchText);
            // renderer.getPreferredSize().height != 0
            if ((this.height == 0) && (renderer.getPreferredSize().height > 0)) {
                this.height = renderer.getPreferredSize().height;
            }

            // Controlla se il valore contiene il testo del campo di testo
            if (!((String) value).toLowerCase().contains(searchText.toLowerCase())) {
                // System.out.println("nascondo " + value);
                // value = "";
                renderer.setVisible(false);
                // renderer.setMaximumSize(new Dimension(1, 1));
                renderer.setPreferredSize(new Dimension(0, 0));
                // renderer.setBackground(Color.RED);

            } else {
                renderer.setVisible(true);
                // System.out.println("mostro " + value);
                int hh = 35;
                if (this.height > 0) {
                    hh = this.height;
                }
                renderer.setPreferredSize(new Dimension(220, hh));
                // renderer.setBackground(Color.GREEN);
            }

            return renderer;
            // return super.getListCellRendererComponent(list, value, index, isSelected,
            // cellHasFocus);
        }
    }

    /**
     * A custom JPanel for displaying a pie chart from a map of data.
     */
    static class PieChartPanel extends JPanel {
        private final Map<String, Double> data;
        private final List<Color> sliceColors = new ArrayList<>();
        private final Map<String, Double> sortedData;

        public PieChartPanel(Map<String, Double> data) {
            this.data = data;
            // Ordina i dati per valore (percentuale) in ordine decrescente per
            // visualizzare il grafico e la legenda in modo ordinato.
            this.sortedData = data.entrySet().stream()
                    .sorted(Map.Entry.<String, Double>comparingByValue().reversed())
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            Map.Entry::getValue,
                            (e1, e2) -> e1,
                            LinkedHashMap::new));

            // Imposta una dimensione minima e preferita per garantire che il pannello sia
            // visibile nello JSplitPane.
            setPreferredSize(new Dimension(400, 300));
            setMinimumSize(new Dimension(250, 200));

            // Generate some deterministic but varied colors
            for (int i = 0; i < this.sortedData.size(); i++) {
                // Use HSB color model for more pleasant, distinct colors
                float hue = (float) i / this.sortedData.size();
                sliceColors.add(Color.getHSBColor(hue, 0.8f, 0.9f));
            }
        }

        @Override
        protected void paintComponent(Graphics g) {
            super.paintComponent(g);
            if (data == null || data.isEmpty()) {
                g.drawString("Nessun dato da visualizzare.", 10, 20);
                return;
            }

            double total = sortedData.values().stream()
                    .filter(v -> v > 0) // Consider only positive values for the chart total
                    .mapToDouble(Double::doubleValue).sum();

            if (total <= 0) {
                g.drawString("Il totale è zero o negativo, impossibile creare il grafico.", 10, 20);
                return;
            }

            Graphics2D g2d = (Graphics2D) g.create();
            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

            int width = getWidth();
            int height = getHeight();

            int legendWidth = 200; // Spazio riservato per la legenda
            int chartAreaWidth = width - legendWidth;

            if (chartAreaWidth <= 0)
                chartAreaWidth = width;

            int pieSize = Math.min(chartAreaWidth, height) - 40;
            if (pieSize >= 20) { // Disegna solo se c'è abbastanza spazio
                int x = (chartAreaWidth - pieSize) / 2;
                int y = (height - pieSize) / 2;

                double currentAngle = 90.0; // Inizia dall'alto
                int colorIndex = 0;

                for (Map.Entry<String, Double> entry : sortedData.entrySet()) {
                    if (entry.getValue() <= 0)
                        continue;
                    double sliceAngle = (entry.getValue() / total) * 360.0;

                    g2d.setColor(sliceColors.get(colorIndex % sliceColors.size()));
                    g2d.fillArc(x, y, pieSize, pieSize, (int) currentAngle, (int) -Math.ceil(sliceAngle));

                    currentAngle -= sliceAngle;
                    colorIndex++;
                }
            }

            // Draw legend
            int legendX = chartAreaWidth + 10;
            int legendY = 20;
            g2d.setFont(new Font("Arial", Font.PLAIN, 12));
            FontMetrics fm = g2d.getFontMetrics();
            int availableTextWidth = width - (legendX + 20);
            int lineHeight = fm.getHeight();

            int colorIndex = 0;
            for (Map.Entry<String, Double> entry : sortedData.entrySet()) {
                if (legendY > getHeight() - 20) {
                    g2d.setColor(Color.BLACK);
                    g2d.drawString("...", legendX, legendY);
                    break;
                }
                if (entry.getValue() <= 0)
                    continue;

                g2d.setColor(sliceColors.get(colorIndex % sliceColors.size()));
                g2d.fillRect(legendX, legendY, 15, 10);
                g2d.setColor(Color.BLACK);

                double percentage = (entry.getValue() / total) * 100;
                String fullText = String.format(Locale.ITALIAN, "%s (%.2f%%)", entry.getKey(), percentage);

                List<String> linesToDraw = new ArrayList<>();
                if (availableTextWidth > 20 && fm.stringWidth(fullText) > availableTextWidth) {
                    String[] words = fullText.split(" ");
                    StringBuilder currentLine = new StringBuilder();
                    if (words.length > 0) {
                        currentLine.append(words[0]);
                        for (int i = 1; i < words.length; i++) {
                            if (fm.stringWidth(currentLine.toString() + " " + words[i]) < availableTextWidth) {
                                currentLine.append(" ").append(words[i]);
                            } else {
                                linesToDraw.add(currentLine.toString());
                                currentLine = new StringBuilder(words[i]);
                            }
                        }
                    }
                    if (currentLine.length() > 0) {
                        linesToDraw.add(currentLine.toString());
                    }
                } else {
                    linesToDraw.add(fullText);
                }

                int textY = legendY + 10;
                for (String line : linesToDraw) {
                    g2d.drawString(line, legendX + 20, textY);
                    textY += lineHeight;
                }

                if (linesToDraw.size() > 1) {
                    legendY += (linesToDraw.size() - 1) * lineHeight;
                }
                legendY += 20; // Standard gap
                colorIndex++;
            }
            g2d.dispose();
        }
    }
}
